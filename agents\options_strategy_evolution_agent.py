#!/usr/bin/env python3
"""
Options Strategy Evolution Agent - Modular Version

This is the main orchestrator for the modular options strategy evolution system.
It uses the new modular architecture with focused components for better maintainability.

Key Features:
- Modular architecture with focused components
- Efficient data usage from existing features data
- Direct backtesting agent integration
- Performance-based genetic algorithm operations
- Real-time strategy adaptation
- Population management and diversity maintenance

The agent maintains a population of strategies and continuously evolves them
to find optimal trading approaches for different market conditions.
"""

import asyncio
import logging
import json
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import aiofiles

# Import modular evolution components
from agents.evolution import (
    DataIntegrationManager,
    PerformanceAnalyzer,
    GeneticOperations,
    StrategyManager,
    EvolutionCore
)
from agents.evolution.strategy_management import StrategyConfig, StrategyStatus
from agents.strategy_generation.data_models import StrategyDefinition

logger = logging.getLogger(__name__)

class OptionsStrategyEvolutionAgent:
    """Main orchestrator for options strategy evolution using modular architecture"""
    
    def __init__(self, config: Dict[str, Any] = None, strategy_definitions_path: Optional[str] = None):
        self.config = config or self._get_default_config()
        self.is_initialized = False
        self.strategy_definitions_path = Path(strategy_definitions_path) if strategy_definitions_path else None

        # Initialize evolution core (components will be accessed after core is initialized)
        self.evolution_core = EvolutionCore(self.config)
        
        # Strategy definitions for evolution
        self.strategy_definitions: List[StrategyDefinition] = []
        
        # Quick access to modules (will be set after evolution_core is initialized)
        self.data_manager: Optional[DataIntegrationManager] = None
        self.performance_analyzer: Optional[PerformanceAnalyzer] = None
        self.genetic_ops: Optional[GeneticOperations] = None
        self.strategy_manager: Optional[StrategyManager] = None
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'population_size': 1000,
            'mutation_rate': 0.15,
            'crossover_rate': 0.8,
            'selection_pressure': 0.3,
            'evolution_interval': 1,  # 5 seconds (debug mode)
            'performance_check_interval': 1,  # 5 seconds (debug mode)
            'debug_mode': True,  # Enable debug mode for development
            'max_workers': 8,
            'timeframes': ['1min', '3min', '5min', '15min'],
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'backtest_days': 30,
            'auto_start_evolution': True,
            'export_strategies': True,
            'export_path': 'config/options_strategies.yaml',
            'buy_only_mode': True # New configuration parameter
        }

    def _is_buy_only_strategy(self, strategy_config: Dict[str, Any]) -> bool:
        """
        Checks if a strategy is compatible with options buying only.
        A strategy is considered buy-only if its 'action' in entry_conditions is 'BUY_CE' or 'BUY_PE',
        or if its name/description explicitly indicates a long/buy position and not a short/sell position.
        """
        strategy_id = strategy_config.get('strategy_id', '').lower()
        name = strategy_config.get('name', '').lower()
        description = strategy_config.get('description', '').lower()
        
        # Check entry conditions for explicit BUY actions
        entry_conditions = strategy_config.get('entry_conditions', [])
        for condition in entry_conditions:
            if isinstance(condition, dict) and condition.get('action') in ['BUY_CE', 'BUY_PE']:
                return True
            # Also check for 'action' within parameters if structured differently
            if isinstance(condition, str) and ('buy_ce' in condition or 'buy_pe' in condition):
                return True

        # Check strategy name/description for keywords
        if ('long' in name or 'buy' in name or 'call' in name or 'put' in name) and \
           ('short' not in name and 'sell' not in name and 'bear' not in name and 'put_spread' not in name and 'call_spread' not in name):
            return True
        
        if ('long' in description or 'buy' in description or 'call' in description or 'put' in description) and \
           ('short' not in description and 'sell' not in description and 'bear' not in description and 'put_spread' not in description and 'call_spread' not in description):
            return True

        # Default to false if no clear buy indication or if it's a spread/short strategy
        return False
    
    async def initialize(self, **kwargs) -> bool:
        """Initialize the evolution agent"""
        try:
            logger.info("[EVOLUTION] Initializing Options Strategy Evolution Agent...")

            # Store kwargs for potential use by evolution core
            self.init_kwargs = kwargs

            # Initialize evolution core, passing the strategy definitions path
            if not await self.evolution_core.initialize(strategy_definitions_path=self.strategy_definitions_path):
                raise RuntimeError("Failed to initialize evolution core")

            # Assign modules after evolution core is initialized
            self.data_manager = self.evolution_core.data_manager
            self.performance_analyzer = self.evolution_core.performance_analyzer
            self.genetic_ops = self.evolution_core.genetic_ops
            self.strategy_manager = self.evolution_core.strategy_manager

            # Load or create initial strategies if registry is empty
            # Check if strategy_manager is not None before accessing strategy_registry
            if self.strategy_manager and len(self.strategy_manager.strategy_registry) == 0:
                await self._load_initial_strategies()
            elif not self.strategy_manager:
                logger.error("[ERROR] Strategy manager is None after evolution core initialization.")
                raise RuntimeError("Strategy manager not initialized.")

            self.is_initialized = True
            logger.info("[EVOLUTION] Options Strategy Evolution Agent initialized successfully")

            # Note: Evolution will be started in the start() method to avoid double-starting

            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution agent initialization failed: {e}")
            return False

    async def start(self, **kwargs) -> bool:
        """Start the evolution agent"""
        try:
            if not self.is_initialized:
                logger.error("[ERROR] Agent not initialized, cannot start")
                return False

            logger.info("[START] Starting Options Strategy Evolution Agent...")

            # Start evolution process for continuous operation
            if self.config.get('auto_start_evolution', True):
                logger.info("[EVOLUTION] Starting evolution loop...")
                # Run evolution in the foreground to keep the agent alive
                await self.start_evolution()

            logger.info("[SUCCESS] Options Strategy Evolution Agent started successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start evolution agent: {e}")
            return False

    async def start_evolution(self):
        """Start the evolution process"""
        try:
            if not self.is_initialized:
                logger.error("[ERROR] Agent not initialized, cannot start evolution")
                return
            
            logger.info("[EVOLUTION] Starting strategy evolution process...")
            await self.evolution_core.start_evolution_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start evolution: {e}")
    
    async def stop_evolution(self):
        """Stop the evolution process"""
        try:
            logger.info("[EVOLUTION] Stopping strategy evolution process...")
            await self.evolution_core.stop_evolution_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop evolution: {e}")
    
    async def trigger_evolution_cycle(self):
        """Manually trigger an evolution cycle"""
        try:
            if not self.is_initialized:
                logger.error("[ERROR] Agent not initialized, cannot trigger evolution")
                return
            
            await self.evolution_core.trigger_evolution_cycle()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to trigger evolution cycle: {e}")
    
    async def add_strategy(self, strategy_config: Dict[str, Any]) -> bool:
        """Add a new strategy to the evolution population"""
        try:
            # Convert dict to StrategyConfig if needed
            if isinstance(strategy_config, dict):
                strategy = StrategyConfig.from_dict(strategy_config)
            else:
                strategy = strategy_config
            
            buy_only_mode = self.config.get('buy_only_mode', False)
            if buy_only_mode and not self._is_buy_only_strategy(strategy.to_dict()):
                logger.warning(f"[EVOLUTION] Cannot add non-buy-only strategy in buy-only mode: {strategy.strategy_id}")
                return False

            return await self.strategy_manager.add_strategy(strategy)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add strategy: {e}")
            return False
    
    async def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy from the evolution population"""
        try:
            return await self.strategy_manager.remove_strategy(strategy_id)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to remove strategy: {e}")
            return False
    
    async def get_strategy_performance(self, strategy_id: str, timeframe: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get performance metrics for a specific strategy.
        If timeframe is provided, returns performance for that specific timeframe.
        Otherwise, returns a dictionary of performances by timeframe.
        """
        try:
            if timeframe:
                cache_key = f"{strategy_id}_{timeframe}"
                if cache_key in self.performance_analyzer.performance_cache:
                    metrics = self.performance_analyzer.performance_cache[cache_key]
                    return self.performance_analyzer.get_performance_summary(metrics)
                else:
                    logger.warning(f"[WARNING] No performance data found for strategy {strategy_id} on {timeframe}")
                    return None
            else:
                # Return all available performance data for the strategy across timeframes
                all_performances = {}
                for cache_key, metrics in self.performance_analyzer.performance_cache.items():
                    if metrics.strategy_id == strategy_id:
                        all_performances[metrics.timeframe] = self.performance_analyzer.get_performance_summary(metrics)
                
                if all_performances:
                    return all_performances
                else:
                    logger.warning(f"[WARNING] No performance data found for strategy {strategy_id} across any timeframe.")
                    return None
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to get strategy performance: {e}")
            return None
    
    async def get_top_strategies(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top performing strategies, considering performance across multiple timeframes"""
        try:
            all_strategies = list(self.strategy_manager.strategy_registry.values())
            
            if not all_strategies:
                return []

            # Aggregate performance for each strategy across all timeframes
            strategy_aggregate_performance: Dict[str, List[Dict[str, Any]]] = {}
            for cache_key, metrics in self.performance_analyzer.performance_cache.items():
                strategy_id = metrics.strategy_id
                if strategy_id not in strategy_aggregate_performance:
                    strategy_aggregate_performance[strategy_id] = []
                strategy_aggregate_performance[strategy_id].append(metrics.to_dict())

            # Calculate an average composite score for each strategy
            strategy_scores = []
            for strategy in all_strategies:
                strategy_id = strategy.strategy_id
                performances = strategy_aggregate_performance.get(strategy_id, [])
                
                if not performances:
                    # If no performance data, assign a very low score
                    avg_composite_score = -float('inf')
                else:
                    # Calculate average composite score across all timeframes
                    total_composite_score = sum(p.get('composite_score', 0) for p in performances)
                    avg_composite_score = total_composite_score / len(performances)
                
                strategy_scores.append((strategy, avg_composite_score))

            # Sort strategies by their average composite score
            strategy_scores.sort(key=lambda x: x[1], reverse=True)

            # Select top performers based on the aggregated score
            top_performers_with_scores = strategy_scores[:limit]
            
            top_strategies_with_performance = []
            for strategy, avg_score in top_performers_with_scores:
                strategy_id = strategy.strategy_id
                # Retrieve all performance metrics for this strategy
                all_timeframe_performances = strategy_aggregate_performance.get(strategy_id, [])
                
                strategy_info = {
                    'strategy': strategy.to_dict(),
                    'aggregated_performance_score': avg_score,
                    'performance_by_timeframe': all_timeframe_performances # Include detailed performance
                }
                top_strategies_with_performance.append(strategy_info)
            
            return top_strategies_with_performance
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get top strategies: {e}")
            return []
    
    async def get_evolution_status(self) -> Dict[str, Any]:
        """Get current evolution status"""
        try:
            return await self.evolution_core.get_evolution_status()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get evolution status: {e}")
            return {'error': str(e)}
    
    async def export_strategies(self, output_path: str = None) -> bool:
        """Export evolved strategies to YAML file"""
        try:
            export_path = output_path or self.config.get('export_path', 'config/options_strategies.yaml')
            return await self.strategy_manager.export_strategies_to_yaml(export_path)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to export strategies: {e}")
            return False
    
    async def get_available_timeframes(self) -> List[str]:
        """Get available timeframes from features data"""
        try:
            return await self.data_manager.get_available_timeframes()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get available timeframes: {e}")
            return []
    
    async def get_data_summary(self, timeframe: str = "5min") -> Dict[str, Any]:
        """Get summary of available data"""
        try:
            return await self.data_manager.get_data_summary(timeframe)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get data summary: {e}")
            return {}
    
    async def _load_initial_strategies(self):
        """Load initial strategies from the main strategy_definitions.json file."""
        try:
            logger.info("[EVOLUTION] Loading initial strategy population...")
            
            initial_strategies_data = await self._load_current_strategy_definitions()

            if not initial_strategies_data:
                logger.warning("No strategy definitions found in strategy_definitions.json, creating a default set if needed.")
                # In a real scenario, you might want to generate a few default strategies here
                # For now, we'll just return if no strategies are found.
                return

            added_count = 0
            buy_only_mode = self.config.get('buy_only_mode', False)

            for strategy_dict in initial_strategies_data:
                # Ensure the dictionary conforms to StrategyConfig structure
                strategy_config = {
                    'strategy_id': strategy_dict.get('strategy_id', f"strategy_{added_count}"),
                    'name': strategy_dict.get('name', 'Unnamed Strategy'),
                    'description': strategy_dict.get('description', ''),
                    'parameters': strategy_dict.get('parameters', {}), # Assuming parameters might be nested
                    'entry_conditions': strategy_dict.get('entry_conditions', []),
                    'exit_conditions': strategy_dict.get('exit_conditions', []),
                    'risk_management': strategy_dict.get('risk_management', {}),
                    'market_outlook': strategy_dict.get('market_outlook', 'neutral'),
                    'volatility_outlook': strategy_dict.get('volatility_outlook', 'normal'),
                    'timeframe': strategy_dict.get('timeframe', '5min'),
                    'status': strategy_dict.get('status', 'experimental'),
                    'tags': strategy_dict.get('tags', ['loaded'])
                }
                
                # Filter strategies if buy_only_mode is enabled
                if buy_only_mode and not self._is_buy_only_strategy(strategy_config):
                    logger.info(f"[EVOLUTION] Skipping non-buy-only strategy: {strategy_config['strategy_id']}")
                    continue

                strategy = StrategyConfig.from_dict(strategy_config)
                if await self.strategy_manager.add_strategy(strategy):
                    added_count += 1

            logger.info(f"[EVOLUTION] Loaded {added_count} initial strategies from strategy_definitions.json")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load initial strategies: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            await self.evolution_core.cleanup()
            logger.info("[EVOLUTION] Evolution agent cleanup completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution agent cleanup failed: {e}")

    async def load_strategy_definitions(self) -> bool:
        """Load strategy definitions for evolution from the main strategy_definitions.json file."""
        try:
            logger.info("[EVOLUTION] Loading strategy definitions for evolution...")
            definitions_data = await self._load_current_strategy_definitions()

            if not definitions_data:
                logger.warning("No strategy definition data found in strategy_definitions.json for evolution.")
                return False

            # Convert to StrategyDefinition objects with proper deserialization
            self.strategy_definitions = []
            for def_data in definitions_data:
                try:
                    # Properly deserialize selection criteria
                    selection_criteria = {}
                    for leg_name, criteria_data in def_data.get('selection_criteria', {}).items():
                        from agents.strategy_generation.data_models import OptionSelectionCriteria
                        criteria = OptionSelectionCriteria(
                            moneyness=criteria_data['moneyness'],
                            dte_range=criteria_data['dte_range'],
                            delta_range=criteria_data.get('delta_range'),
                            strike_range=criteria_data.get('strike_range'),
                            volume_threshold=criteria_data.get('volume_threshold'),
                            open_interest_threshold=criteria_data.get('open_interest_threshold'),
                            max_bid_ask_spread=criteria_data.get('max_bid_ask_spread')
                        )
                        selection_criteria[leg_name] = criteria

                    # Create StrategyDefinition with proper types
                    from agents.strategy_generation.data_models import StrategyType
                    strategy_type = StrategyType(def_data['strategy_type']) if isinstance(def_data['strategy_type'], str) else def_data['strategy_type']

                    strategy_def = StrategyDefinition(
                        strategy_id=def_data['strategy_id'],
                        strategy_type=strategy_type,
                        underlying=def_data['underlying'],
                        name=def_data['name'],
                        description=def_data['description'],
                        selection_criteria=selection_criteria,
                        entry_conditions=def_data['entry_conditions'],
                        exit_conditions=def_data['exit_conditions'],
                        risk_management=def_data['risk_management'],
                        market_outlook=def_data['market_outlook'],
                        volatility_outlook=def_data['volatility_outlook'],
                        timeframe=def_data['timeframe'],
                        created_at=datetime.fromisoformat(def_data['created_at']),
                        tags=def_data['tags']
                    )
                    self.strategy_definitions.append(strategy_def)
                    logger.debug(f"[EVOLUTION] Loaded strategy definition: {strategy_def.strategy_id}")
                except Exception as e:
                    logger.error(f"Failed to load strategy definition {def_data.get('strategy_id', 'unknown')}: {e}")
                    import traceback
                    logger.debug(f"Error details: {traceback.format_exc()}")

            logger.info(f"Loaded {len(self.strategy_definitions)} strategy definitions for evolution")
            return True

        except Exception as e:
            logger.error(f"Failed to load strategy definitions: {e}")
            return False

    async def evolve_strategy_definitions(self) -> List[StrategyDefinition]:
        """
        Evolve strategy definitions by modifying selection criteria and conditions

        This method:
        1. Analyzes performance of existing strategy definitions
        2. Creates mutations of successful strategies
        3. Generates new strategy definitions with evolved parameters
        """
        try:
            if not self.strategy_definitions:
                logger.warning("No strategy definitions to evolve")
                return []

            logger.info(f"Starting evolution of {len(self.strategy_definitions)} strategy definitions")

            # Analyze performance of current strategy definitions
            performance_data = await self._analyze_strategy_definition_performance()

            # Select best performing strategies for evolution
            top_strategies = await self._select_top_strategy_definitions(performance_data)

            # Create evolved versions
            evolved_definitions = []

            for strategy_def in top_strategies:
                # Create mutations of the strategy definition
                mutations = await self._mutate_strategy_definition(strategy_def)
                evolved_definitions.extend(mutations)

            # Add some completely new strategy definitions
            new_definitions = await self._generate_new_strategy_definitions()
            evolved_definitions.extend(new_definitions)

            logger.info(f"Generated {len(evolved_definitions)} evolved strategy definitions")

            # Save evolved definitions
            await self._save_evolved_strategy_definitions(evolved_definitions)

            return evolved_definitions

        except Exception as e:
            logger.error(f"Failed to evolve strategy definitions: {e}")
            return []

    async def _analyze_strategy_definition_performance(self) -> Dict[str, Dict]:
        """Analyze performance of strategy definitions using backtesting results"""
        try:
            performance_data = {}

            # Try to load recent backtesting results
            backtest_results = await self._load_recent_backtest_results()

            if backtest_results:
                logger.info(f"[EVOLUTION] Found backtest results for {len(backtest_results)} strategies")

                for strategy_id, results in backtest_results.items():
                    if hasattr(results, 'total_return'):
                        performance_data[strategy_id] = {
                            'total_return': results.total_return,
                            'sharpe_ratio': results.sharpe_ratio,
                            'win_rate': results.win_rate,
                            'max_drawdown': results.max_drawdown,
                            'total_trades': results.total_trades,
                            'profit_factor': results.profit_factor,
                            'avg_trade_duration': getattr(results, 'avg_trade_duration', 0),
                            'volatility': getattr(results, 'volatility', 0)
                        }
                    else:
                        # Handle dictionary format
                        performance_data[strategy_id] = results
            else:
                logger.warning("[EVOLUTION] No backtest results found, using synthetic performance data")
                # Generate synthetic performance data for testing
                import random

                for strategy_def in self.strategy_definitions:
                    # Generate realistic but random performance metrics
                    base_return = random.uniform(-0.2, 0.4)  # -20% to 40% return
                    volatility = random.uniform(0.15, 0.35)

                    performance_data[strategy_def.strategy_id] = {
                        'total_return': base_return,
                        'sharpe_ratio': base_return / volatility if volatility > 0 else 0,
                        'win_rate': random.uniform(0.4, 0.8),
                        'max_drawdown': random.uniform(0.05, 0.25),
                        'total_trades': random.randint(20, 100),
                        'profit_factor': random.uniform(0.8, 2.5),
                        'avg_trade_duration': random.uniform(0.5, 5.0),  # days
                        'volatility': volatility
                    }

            logger.info(f"[EVOLUTION] Analyzed performance for {len(performance_data)} strategy definitions")
            return performance_data

        except Exception as e:
            logger.error(f"Failed to analyze strategy definition performance: {e}")
            return {}

    async def _select_top_strategy_definitions(self, performance_data: Dict) -> List[StrategyDefinition]:
        """Select top performing strategy definitions for evolution using multi-criteria scoring"""
        try:
            if not performance_data:
                logger.warning("[EVOLUTION] No performance data available, selecting random strategies")
                import random
                return random.sample(self.strategy_definitions, min(3, len(self.strategy_definitions)))

            # Calculate composite fitness scores for each strategy
            strategy_scores = []

            for strategy_def in self.strategy_definitions:
                perf = performance_data.get(strategy_def.strategy_id, {})

                # Multi-criteria fitness function
                total_return = perf.get('total_return', 0)
                sharpe_ratio = perf.get('sharpe_ratio', 0)
                win_rate = perf.get('win_rate', 0)
                max_drawdown = perf.get('max_drawdown', 1)
                profit_factor = perf.get('profit_factor', 0)
                total_trades = perf.get('total_trades', 0)

                # Normalize and weight different metrics for improved quality focus
                return_score = max(0, total_return) * 0.25  # Reduced weight on raw returns
                sharpe_score = max(0, sharpe_ratio / 3.0) * 0.30  # Increased weight on risk-adjusted returns
                win_rate_score = win_rate * 0.15  # Reduced weight on win rate
                drawdown_score = max(0, (0.2 - max_drawdown) / 0.2) * 0.20  # Increased weight on drawdown control
                profit_factor_score = min(1.0, profit_factor / 2.0) * 0.1  # Same weight

                # Bonus for sufficient trading activity
                activity_bonus = min(1.0, total_trades / 30.0) * 0.05 if total_trades > 10 else 0

                composite_score = (return_score + sharpe_score + win_rate_score +
                                 drawdown_score + profit_factor_score + activity_bonus)

                strategy_scores.append((strategy_def, composite_score, perf))

            # Sort by composite score
            strategy_scores.sort(key=lambda x: x[1], reverse=True)

            # Log top performers
            logger.info("[EVOLUTION] Top performing strategies:")
            for i, (strategy_def, score, perf) in enumerate(strategy_scores[:5]):
                logger.info(f"  {i+1}. {strategy_def.strategy_id} (Score: {score:.3f})")
                logger.info(f"     Return: {perf.get('total_return', 0):.2%}, "
                          f"Sharpe: {perf.get('sharpe_ratio', 0):.2f}, "
                          f"Win Rate: {perf.get('win_rate', 0):.2%}")

            # Select top strategies for evolution
            # Take top 40% but ensure minimum diversity
            top_count = max(2, min(len(strategy_scores) // 2, len(strategy_scores)))
            selected_strategies = [item[0] for item in strategy_scores[:top_count]]

            # Ensure diversity by including strategies from different underlyings/timeframes
            selected_strategies = await self._ensure_strategy_diversity(selected_strategies)

            logger.info(f"[EVOLUTION] Selected {len(selected_strategies)} strategies for evolution")
            return selected_strategies

        except Exception as e:
            logger.error(f"Failed to select top strategy definitions: {e}")
            return []

    async def _mutate_strategy_definition(self, strategy_def: StrategyDefinition) -> List[StrategyDefinition]:
        """Create mutations of a strategy definition"""
        try:
            mutations = []

            # Create 2-3 mutations per strategy
            for i in range(3):
                mutated_def = await self._create_mutated_strategy_definition(strategy_def, i)
                if mutated_def:
                    mutations.append(mutated_def)

            return mutations

        except Exception as e:
            logger.error(f"Failed to mutate strategy definition {strategy_def.strategy_id}: {e}")
            return []

    async def _create_mutated_strategy_definition(self, original: StrategyDefinition, mutation_id: int) -> Optional[StrategyDefinition]:
        """Create a single mutated strategy definition with comprehensive mutations"""
        try:
            import copy
            import random
            from agents.strategy_generation.data_models import OptionSelectionCriteria

            # Deep copy the original
            mutated = copy.deepcopy(original)

            # Generate new unique ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            mutated.strategy_id = f"{original.strategy_id}_mut_{mutation_id}_{timestamp}"
            mutated.name = f"{original.name} (Evolved {mutation_id})"
            mutated.description = f"Evolved version of {original.description}"
            mutated.created_at = datetime.now()
            mutated.tags = original.tags + ["evolved", f"generation_{mutation_id}"]

            # Apply different types of mutations based on mutation_id
            mutation_applied = False

            if mutation_id == 0:
                # Mutation Type 1: Modify selection criteria
                mutation_applied = await self._mutate_selection_criteria(mutated)

            elif mutation_id == 1:
                # Mutation Type 2: Modify entry/exit conditions
                mutation_applied = await self._mutate_trading_conditions(mutated)

            elif mutation_id == 2:
                # Mutation Type 3: Modify risk management
                mutation_applied = await self._mutate_risk_management(mutated)

            if mutation_applied:
                logger.debug(f"[EVOLUTION] Created mutation {mutation_id} for {original.strategy_id}")
                return mutated
            else:
                logger.warning(f"[EVOLUTION] Failed to apply mutation {mutation_id} to {original.strategy_id}")
                return None

        except Exception as e:
            logger.error(f"Failed to create mutated strategy definition: {e}")
            return None

    async def _generate_new_strategy_definitions(self) -> List[StrategyDefinition]:
        """Generate completely new strategy definitions"""
        try:
            # This would use the strategy definition generator
            # For now, return empty list
            return []

        except Exception as e:
            logger.error(f"Failed to generate new strategy definitions: {e}")
            return []

    async def _serialize_strategy_definition(self, strategy_def: StrategyDefinition) -> Dict[str, Any]:
        """Properly serialize a strategy definition to dictionary format"""
        try:
            # Serialize selection criteria
            selection_criteria_dict = {}
            for leg_name, criteria in strategy_def.selection_criteria.items():
                selection_criteria_dict[leg_name] = {
                    'moneyness': criteria.moneyness,
                    'dte_range': criteria.dte_range,
                    'delta_range': criteria.delta_range,
                    'strike_range': criteria.strike_range,
                    'volume_threshold': criteria.volume_threshold,
                    'open_interest_threshold': criteria.open_interest_threshold,
                    'max_bid_ask_spread': criteria.max_bid_ask_spread
                }

            return {
                'strategy_id': strategy_def.strategy_id,
                'strategy_type': strategy_def.strategy_type.value if hasattr(strategy_def.strategy_type, 'value') else str(strategy_def.strategy_type),
                'underlying': strategy_def.underlying,
                'name': strategy_def.name,
                'description': strategy_def.description,
                'selection_criteria': selection_criteria_dict,
                'entry_conditions': strategy_def.entry_conditions,
                'exit_conditions': strategy_def.exit_conditions,
                'risk_management': strategy_def.risk_management,
                'market_outlook': strategy_def.market_outlook,
                'volatility_outlook': strategy_def.volatility_outlook,
                'timeframe': strategy_def.timeframe,
                'created_at': strategy_def.created_at.isoformat(),
                'tags': strategy_def.tags
            }

        except Exception as e:
            logger.error(f"Failed to serialize strategy definition {strategy_def.strategy_id}: {e}")
            return {}

    async def _get_main_strategy_definitions_file_path(self) -> Path:
        """Get the path to the main strategy definitions file."""
        if self.strategy_definitions_path:
            return self.strategy_definitions_path
        return Path("data/strategies") / "strategy_definitions.json"

    async def _load_current_strategy_definitions(self) -> List[Dict[str, Any]]:
        """Load current strategy definitions from the main file."""
        filepath = await self._get_main_strategy_definitions_file_path()
        if not filepath.exists():
            logger.warning(f"Strategy definitions file not found at {filepath}. Returning empty list.")
            return []
        async with aiofiles.open(filepath, 'r') as f:
            content = await f.read()
            return json.loads(content)

    async def _save_strategy_definitions_to_file(self, definitions: List[Dict[str, Any]]):
        """Save strategy definitions to the main file."""
        filepath = await self._get_main_strategy_definitions_file_path()
        filepath.parent.mkdir(parents=True, exist_ok=True)
        async with aiofiles.open(filepath, 'w') as f:
            await f.write(json.dumps(definitions, indent=2, default=str))
        logger.info(f"[EVOLUTION] Saved strategy definitions to {filepath}")

    async def _save_evolved_strategy_definitions(self, evolved_definitions: List[StrategyDefinition]):
        """Save evolved strategy definitions by updating the main strategy_definitions.json file."""
        try:
            if not evolved_definitions:
                return

            current_definitions = await self._load_current_strategy_definitions()
            current_strategies_map = {def_data['strategy_id']: def_data for def_data in current_definitions}

            updated_count = 0
            added_count = 0

            for evolved_def in evolved_definitions:
                evolved_dict = await self._serialize_strategy_definition(evolved_def)
                if evolved_def.strategy_id in current_strategies_map:
                    current_strategies_map[evolved_def.strategy_id] = evolved_dict
                    updated_count += 1
                else:
                    current_strategies_map[evolved_def.strategy_id] = evolved_dict
                    added_count += 1

            updated_definitions = list(current_strategies_map.values())
            await self._save_strategy_definitions_to_file(updated_definitions)

            logger.info(f"[EVOLUTION] Updated {updated_count} existing strategies and added {added_count} new evolved strategies to strategy_definitions.json")

        except Exception as e:
            logger.error(f"Failed to save evolved strategy definitions: {e}")

    async def modify_strategy_definition_file(self, strategy_id: str, modifications: Dict[str, Any]) -> bool:
        """
        Modify a specific strategy definition in the main strategy_definitions.json file.

        Args:
            strategy_id: ID of the strategy to modify
            modifications: Dictionary of modifications to apply

        Returns:
            bool: True if modification was successful
        """
        try:
            definitions = await self._load_current_strategy_definitions()
            strategy_found = False

            for def_data in definitions:
                if def_data['strategy_id'] == strategy_id:
                    strategy_found = True
                    for key, value in modifications.items():
                        if key in def_data:
                            old_value = def_data[key]
                            def_data[key] = value
                            logger.info(f"[EVOLUTION] Modified {strategy_id}.{key}: {old_value} -> {value}")
                        else:
                            def_data[key] = value
                            logger.info(f"[EVOLUTION] Added {strategy_id}.{key}: {value}")
                    def_data['last_modified'] = datetime.now().isoformat()
                    break

            if not strategy_found:
                logger.error(f"[EVOLUTION] Strategy {strategy_id} not found in definitions file")
                return False

            await self._save_strategy_definitions_to_file(definitions)
            logger.info(f"[EVOLUTION] Modified strategy {strategy_id} in strategy_definitions.json")
            return True

        except Exception as e:
            logger.error(f"Failed to modify strategy definition file: {e}")
            return False

    async def add_strategy_definition_to_file(self, strategy_def: StrategyDefinition) -> bool:
        """
        Add a new strategy definition to the main strategy_definitions.json file.

        Args:
            strategy_def: StrategyDefinition to add

        Returns:
            bool: True if addition was successful
        """
        try:
            current_definitions = await self._load_current_strategy_definitions()
            existing_ids = {def_data['strategy_id'] for def_data in current_definitions}

            if strategy_def.strategy_id in existing_ids:
                logger.warning(f"[EVOLUTION] Strategy {strategy_def.strategy_id} already exists, use modify instead")
                return False

            new_strategy_dict = await self._serialize_strategy_definition(strategy_def)
            current_definitions.append(new_strategy_dict)

            await self._save_strategy_definitions_to_file(current_definitions)
            logger.info(f"[EVOLUTION] Added strategy {strategy_def.strategy_id} to strategy_definitions.json")
            return True

        except Exception as e:
            logger.error(f"Failed to add strategy definition to file: {e}")
            return False

    async def remove_strategy_definition_from_file(self, strategy_id: str) -> bool:
        """
        Remove a strategy definition from the main strategy_definitions.json file.

        Args:
            strategy_id: ID of the strategy to remove

        Returns:
            bool: True if removal was successful
        """
        try:
            definitions = await self._load_current_strategy_definitions()
            original_count = len(definitions)
            definitions = [def_data for def_data in definitions if def_data['strategy_id'] != strategy_id]

            if len(definitions) == original_count:
                logger.warning(f"[EVOLUTION] Strategy {strategy_id} not found in definitions file")
                return False

            await self._save_strategy_definitions_to_file(definitions)
            logger.info(f"[EVOLUTION] Removed strategy {strategy_id} from strategy_definitions.json")
            return True

        except Exception as e:
            logger.error(f"Failed to remove strategy definition from file: {e}")
            return False

    async def _mutate_selection_criteria(self, strategy_def: StrategyDefinition) -> bool:
        """Mutate option selection criteria"""
        try:
            import random

            mutation_applied = False

            for leg_name, criteria in strategy_def.selection_criteria.items():
                # Randomly decide whether to mutate this leg (50% chance)
                if random.random() < 0.5:
                    continue

                # Mutate DTE range
                if criteria.dte_range and random.random() < 0.7:
                    min_dte, max_dte = criteria.dte_range
                    # Adjust DTE range by ±20%
                    adjustment = random.uniform(0.8, 1.2)
                    new_min = max(1, int(min_dte * adjustment))
                    new_max = max(new_min + 1, int(max_dte * adjustment))
                    criteria.dte_range = [new_min, new_max]
                    mutation_applied = True
                    logger.debug(f"[MUTATION] Modified DTE range for {leg_name}: {criteria.dte_range}")

                # Mutate delta range
                if criteria.delta_range and random.random() < 0.6:
                    min_delta, max_delta = criteria.delta_range
                    # Adjust delta range by ±10%
                    adjustment = random.uniform(0.9, 1.1)
                    new_min = max(0.01, min(0.99, min_delta * adjustment))
                    new_max = max(new_min + 0.01, min(1.0, max_delta * adjustment))
                    criteria.delta_range = [round(new_min, 2), round(new_max, 2)]
                    mutation_applied = True
                    logger.debug(f"[MUTATION] Modified delta range for {leg_name}: {criteria.delta_range}")

                # Mutate volume threshold
                if criteria.volume_threshold and random.random() < 0.5:
                    # Adjust volume threshold by ±30%
                    adjustment = random.uniform(0.7, 1.3)
                    new_threshold = max(100, int(criteria.volume_threshold * adjustment))
                    criteria.volume_threshold = new_threshold
                    mutation_applied = True
                    logger.debug(f"[MUTATION] Modified volume threshold for {leg_name}: {new_threshold}")

                # Mutate open interest threshold
                if criteria.open_interest_threshold and random.random() < 0.5:
                    # Adjust OI threshold by ±30%
                    adjustment = random.uniform(0.7, 1.3)
                    new_threshold = max(50, int(criteria.open_interest_threshold * adjustment))
                    criteria.open_interest_threshold = new_threshold
                    mutation_applied = True
                    logger.debug(f"[MUTATION] Modified OI threshold for {leg_name}: {new_threshold}")

                # Occasionally change moneyness (10% chance)
                if random.random() < 0.1:
                    moneyness_options = ["ATM", "OTM", "ITM"]
                    if criteria.moneyness in moneyness_options:
                        moneyness_options.remove(criteria.moneyness)
                        criteria.moneyness = random.choice(moneyness_options)
                        mutation_applied = True
                        logger.debug(f"[MUTATION] Changed moneyness for {leg_name}: {criteria.moneyness}")

            return mutation_applied

        except Exception as e:
            logger.error(f"Failed to mutate selection criteria: {e}")
            return False

    async def _mutate_trading_conditions(self, strategy_def: StrategyDefinition) -> bool:
        """Mutate entry and exit conditions"""
        try:
            import random

            mutation_applied = False

            # Mutate entry conditions
            for condition in strategy_def.entry_conditions:
                if random.random() < 0.4:  # 40% chance to mutate each condition
                    if 'value' in condition and isinstance(condition['value'], (int, float)):
                        # Adjust numerical values by ±15%
                        adjustment = random.uniform(0.85, 1.15)
                        old_value = condition['value']
                        condition['value'] = round(old_value * adjustment, 2)
                        mutation_applied = True
                        logger.debug(f"[MUTATION] Modified entry condition value: {old_value} -> {condition['value']}")

            # Mutate exit conditions
            for condition in strategy_def.exit_conditions:
                if random.random() < 0.4:  # 40% chance to mutate each condition
                    if 'value' in condition and isinstance(condition['value'], (int, float)):
                        # Adjust numerical values by ±15%
                        adjustment = random.uniform(0.85, 1.15)
                        old_value = condition['value']
                        condition['value'] = round(old_value * adjustment, 2)
                        mutation_applied = True
                        logger.debug(f"[MUTATION] Modified exit condition value: {old_value} -> {condition['value']}")

            return mutation_applied

        except Exception as e:
            logger.error(f"Failed to mutate trading conditions: {e}")
            return False

    async def _mutate_risk_management(self, strategy_def: StrategyDefinition) -> bool:
        """Mutate risk management parameters"""
        try:
            import random

            mutation_applied = False

            # Mutate stop loss
            if 'stop_loss_pct' in strategy_def.risk_management and random.random() < 0.5:
                old_value = strategy_def.risk_management['stop_loss_pct']
                # Adjust stop loss by ±20%
                adjustment = random.uniform(0.8, 1.2)
                new_value = max(0.05, min(0.5, old_value * adjustment))  # Keep between 5% and 50%
                strategy_def.risk_management['stop_loss_pct'] = round(new_value, 3)
                mutation_applied = True
                logger.debug(f"[MUTATION] Modified stop loss: {old_value} -> {new_value}")

            # Mutate take profit
            if 'take_profit_pct' in strategy_def.risk_management and random.random() < 0.5:
                old_value = strategy_def.risk_management['take_profit_pct']
                # Adjust take profit by ±25%
                adjustment = random.uniform(0.75, 1.25)
                new_value = max(0.1, min(2.0, old_value * adjustment))  # Keep between 10% and 200%
                strategy_def.risk_management['take_profit_pct'] = round(new_value, 3)
                mutation_applied = True
                logger.debug(f"[MUTATION] Modified take profit: {old_value} -> {new_value}")

            return mutation_applied

        except Exception as e:
            logger.error(f"Failed to mutate risk management: {e}")
            return False

    async def _ensure_strategy_diversity(self, strategies: List[StrategyDefinition]) -> List[StrategyDefinition]:
        """Ensure diversity in selected strategies"""
        try:
            # Group by underlying and timeframe
            diversity_groups = {}

            for strategy in strategies:
                key = f"{strategy.underlying}_{strategy.timeframe}"
                if key not in diversity_groups:
                    diversity_groups[key] = []
                diversity_groups[key].append(strategy)

            # Ensure at least one strategy from each group
            diverse_strategies = []
            for group_strategies in diversity_groups.values():
                diverse_strategies.append(group_strategies[0])  # Take first from each group

            # Add remaining strategies up to original count
            remaining_count = len(strategies) - len(diverse_strategies)
            if remaining_count > 0:
                remaining_strategies = [s for s in strategies if s not in diverse_strategies]
                diverse_strategies.extend(remaining_strategies[:remaining_count])

            return diverse_strategies

        except Exception as e:
            logger.error(f"Failed to ensure strategy diversity: {e}")
            return strategies

    async def _load_recent_backtest_results(self) -> Dict[str, Any]:
        """Load recent backtesting results"""
        try:
            # Look for recent backtest result files
            backtest_path = Path("data/backtesting")
            if not backtest_path.exists():
                return {}

            result_files = list(backtest_path.glob("*backtest*.json"))
            if not result_files:
                return {}

            # Load the most recent file
            latest_file = max(result_files, key=lambda x: x.stat().st_mtime)

            async with aiofiles.open(latest_file, 'r') as f:
                content = await f.read()
                results = json.loads(content)

            logger.info(f"[EVOLUTION] Loaded backtest results from {latest_file.name}")
            return results.get('strategy_results', {})

        except Exception as e:
            logger.error(f"Failed to load recent backtest results: {e}")
            return {}

    async def generate_evolution_analytics(self) -> Dict[str, Any]:
        """Generate comprehensive evolution analytics and reports"""
        try:
            analytics = {
                'timestamp': datetime.now().isoformat(),
                'strategy_population': await self._analyze_strategy_population(),
                'performance_metrics': await self._analyze_performance_trends(),
                'evolution_history': await self._analyze_evolution_history(),
                'diversity_metrics': await self._analyze_strategy_diversity(),
                'recommendations': await self._generate_evolution_recommendations()
            }

            # Save analytics to file
            await self._save_evolution_analytics(analytics)

            return analytics

        except Exception as e:
            logger.error(f"Failed to generate evolution analytics: {e}")
            return {}

    async def _analyze_strategy_population(self) -> Dict[str, Any]:
        """Analyze current strategy population"""
        try:
            if not self.strategy_definitions:
                return {'total_strategies': 0}

            # Basic population statistics
            total_strategies = len(self.strategy_definitions)

            # Group by various attributes
            by_underlying = {}
            by_timeframe = {}
            by_strategy_type = {}
            by_market_outlook = {}

            for strategy_def in self.strategy_definitions:
                # Count by underlying
                underlying = strategy_def.underlying
                by_underlying[underlying] = by_underlying.get(underlying, 0) + 1

                # Count by timeframe
                timeframe = strategy_def.timeframe
                by_timeframe[timeframe] = by_timeframe.get(timeframe, 0) + 1

                # Count by strategy type
                strategy_type = strategy_def.strategy_type.value if hasattr(strategy_def.strategy_type, 'value') else str(strategy_def.strategy_type)
                by_strategy_type[strategy_type] = by_strategy_type.get(strategy_type, 0) + 1

                # Count by market outlook
                market_outlook = strategy_def.market_outlook
                by_market_outlook[market_outlook] = by_market_outlook.get(market_outlook, 0) + 1

            return {
                'total_strategies': total_strategies,
                'by_underlying': by_underlying,
                'by_timeframe': by_timeframe,
                'by_strategy_type': by_strategy_type,
                'by_market_outlook': by_market_outlook,
                'creation_dates': [s.created_at.isoformat() for s in self.strategy_definitions]
            }

        except Exception as e:
            logger.error(f"Failed to analyze strategy population: {e}")
            return {}

    async def _analyze_performance_trends(self) -> Dict[str, Any]:
        """Analyze performance trends across strategies"""
        try:
            performance_data = await self._analyze_strategy_definition_performance()

            if not performance_data:
                return {'no_performance_data': True}

            # Calculate aggregate statistics
            returns = [perf.get('total_return', 0) for perf in performance_data.values()]
            sharpe_ratios = [perf.get('sharpe_ratio', 0) for perf in performance_data.values()]
            win_rates = [perf.get('win_rate', 0) for perf in performance_data.values()]
            max_drawdowns = [perf.get('max_drawdown', 0) for perf in performance_data.values()]

            import numpy as np

            trends = {
                'total_return': {
                    'mean': np.mean(returns),
                    'median': np.median(returns),
                    'std': np.std(returns),
                    'min': np.min(returns),
                    'max': np.max(returns),
                    'percentile_25': np.percentile(returns, 25),
                    'percentile_75': np.percentile(returns, 75)
                },
                'sharpe_ratio': {
                    'mean': np.mean(sharpe_ratios),
                    'median': np.median(sharpe_ratios),
                    'std': np.std(sharpe_ratios),
                    'min': np.min(sharpe_ratios),
                    'max': np.max(sharpe_ratios)
                },
                'win_rate': {
                    'mean': np.mean(win_rates),
                    'median': np.median(win_rates),
                    'std': np.std(win_rates),
                    'min': np.min(win_rates),
                    'max': np.max(win_rates)
                },
                'max_drawdown': {
                    'mean': np.mean(max_drawdowns),
                    'median': np.median(max_drawdowns),
                    'std': np.std(max_drawdowns),
                    'min': np.min(max_drawdowns),
                    'max': np.max(max_drawdowns)
                }
            }

            # Identify top and bottom performers
            strategy_performance = [(strategy_id, perf) for strategy_id, perf in performance_data.items()]
            strategy_performance.sort(key=lambda x: x[1].get('total_return', 0), reverse=True)

            top_performers = strategy_performance[:5]
            bottom_performers = strategy_performance[-5:]

            return {
                'aggregate_statistics': trends,
                'top_performers': [{'strategy_id': sid, 'performance': perf} for sid, perf in top_performers],
                'bottom_performers': [{'strategy_id': sid, 'performance': perf} for sid, perf in bottom_performers],
                'total_strategies_analyzed': len(performance_data)
            }

        except Exception as e:
            logger.error(f"Failed to analyze performance trends: {e}")
            return {}

    async def _analyze_evolution_history(self) -> Dict[str, Any]:
        """Analyze evolution history and patterns"""
        try:
            # Look for evolved strategy files
            strategy_path = Path("data/strategies")
            evolved_files = list(strategy_path.glob("evolved_strategy_definitions_*.json"))

            if not evolved_files:
                return {'no_evolution_history': True}

            evolution_history = []

            for evolved_file in sorted(evolved_files, key=lambda x: x.stat().st_mtime):
                try:
                    async with aiofiles.open(evolved_file, 'r') as f:
                        content = await f.read()
                        evolved_strategies = json.loads(content)

                    # Extract timestamp from filename
                    timestamp_str = evolved_file.stem.split('_')[-2] + '_' + evolved_file.stem.split('_')[-1]

                    evolution_history.append({
                        'timestamp': timestamp_str,
                        'file': evolved_file.name,
                        'strategy_count': len(evolved_strategies),
                        'strategy_ids': [s.get('strategy_id', 'unknown') for s in evolved_strategies]
                    })

                except Exception as e:
                    logger.warning(f"Failed to analyze evolution file {evolved_file}: {e}")

            # Analyze evolution patterns
            total_evolutions = len(evolution_history)
            total_evolved_strategies = sum(entry['strategy_count'] for entry in evolution_history)

            # Find most frequently evolved strategies
            all_evolved_ids = []
            for entry in evolution_history:
                all_evolved_ids.extend(entry['strategy_ids'])

            from collections import Counter
            evolution_frequency = Counter(all_evolved_ids)
            most_evolved = evolution_frequency.most_common(10)

            return {
                'total_evolution_cycles': total_evolutions,
                'total_evolved_strategies': total_evolved_strategies,
                'evolution_timeline': evolution_history,
                'most_frequently_evolved': most_evolved,
                'avg_strategies_per_cycle': total_evolved_strategies / max(1, total_evolutions)
            }

        except Exception as e:
            logger.error(f"Failed to analyze evolution history: {e}")
            return {}

    async def _analyze_strategy_diversity(self) -> Dict[str, Any]:
        """Analyze diversity metrics of strategy population"""
        try:
            if not self.strategy_definitions:
                return {'no_strategies': True}

            # Analyze parameter diversity
            dte_ranges = []
            delta_ranges = []
            volume_thresholds = []
            moneyness_types = []

            for strategy_def in self.strategy_definitions:
                for leg_name, criteria in strategy_def.selection_criteria.items():
                    if criteria.dte_range:
                        dte_ranges.append(criteria.dte_range)
                    if criteria.delta_range:
                        delta_ranges.append(criteria.delta_range)
                    if criteria.volume_threshold:
                        volume_thresholds.append(criteria.volume_threshold)
                    if criteria.moneyness:
                        moneyness_types.append(criteria.moneyness)

            # Calculate diversity metrics
            import numpy as np

            diversity_metrics = {
                'dte_range_diversity': {
                    'unique_ranges': len(set(tuple(r) for r in dte_ranges)),
                    'min_dte': min([r[0] for r in dte_ranges]) if dte_ranges else 0,
                    'max_dte': max([r[1] for r in dte_ranges]) if dte_ranges else 0,
                    'avg_dte_span': np.mean([r[1] - r[0] for r in dte_ranges]) if dte_ranges else 0
                },
                'volume_threshold_diversity': {
                    'unique_thresholds': len(set(volume_thresholds)),
                    'min_threshold': min(volume_thresholds) if volume_thresholds else 0,
                    'max_threshold': max(volume_thresholds) if volume_thresholds else 0,
                    'avg_threshold': np.mean(volume_thresholds) if volume_thresholds else 0
                },
                'moneyness_distribution': dict(Counter(moneyness_types)),
                'total_parameter_combinations': len(set(
                    (tuple(r) if r else None, t, m)
                    for r, t, m in zip(dte_ranges, volume_thresholds, moneyness_types)
                ))
            }

            # Calculate diversity score (0-1, higher is more diverse)
            unique_combinations = diversity_metrics['total_parameter_combinations']
            max_possible_combinations = len(self.strategy_definitions)
            diversity_score = unique_combinations / max(1, max_possible_combinations)

            diversity_metrics['overall_diversity_score'] = diversity_score

            return diversity_metrics

        except Exception as e:
            logger.error(f"Failed to analyze strategy diversity: {e}")
            return {}

    async def _generate_evolution_recommendations(self) -> List[Dict[str, Any]]:
        """Generate recommendations for evolution improvements"""
        try:
            recommendations = []

            # Analyze current population
            population_analysis = await self._analyze_strategy_population()
            performance_analysis = await self._analyze_performance_trends()
            diversity_analysis = await self._analyze_strategy_diversity()

            # Recommendation 1: Population balance
            if population_analysis.get('total_strategies', 0) < 10:
                recommendations.append({
                    'type': 'population_size',
                    'priority': 'high',
                    'title': 'Increase Strategy Population',
                    'description': 'Current population is small. Consider generating more diverse strategies.',
                    'action': 'Generate 10-15 additional strategy definitions with varied parameters.'
                })

            # Recommendation 2: Diversity improvement
            diversity_score = diversity_analysis.get('overall_diversity_score', 0)
            if diversity_score < 0.7:
                recommendations.append({
                    'type': 'diversity',
                    'priority': 'medium',
                    'title': 'Improve Parameter Diversity',
                    'description': f'Diversity score is {diversity_score:.2f}. Strategies may be too similar.',
                    'action': 'Increase mutation rates or introduce more varied parameter ranges.'
                })

            # Recommendation 3: Performance-based
            if performance_analysis.get('aggregate_statistics'):
                avg_return = performance_analysis['aggregate_statistics']['total_return']['mean']
                if avg_return < 0.1:  # Less than 10% average return
                    recommendations.append({
                        'type': 'performance',
                        'priority': 'high',
                        'title': 'Improve Strategy Performance',
                        'description': f'Average return is {avg_return:.2%}. Consider more aggressive evolution.',
                        'action': 'Focus evolution on top performers and increase mutation intensity.'
                    })

            # Recommendation 4: Timeframe coverage
            timeframe_dist = population_analysis.get('by_timeframe', {})
            if len(timeframe_dist) < 3:
                recommendations.append({
                    'type': 'coverage',
                    'priority': 'medium',
                    'title': 'Expand Timeframe Coverage',
                    'description': 'Limited timeframe diversity detected.',
                    'action': 'Generate strategies for additional timeframes (1min, 5min, 15min, etc.).'
                })

            # Recommendation 5: Underlying coverage
            underlying_dist = population_analysis.get('by_underlying', {})
            if len(underlying_dist) < 2:
                recommendations.append({
                    'type': 'coverage',
                    'priority': 'medium',
                    'title': 'Expand Underlying Coverage',
                    'description': 'Limited underlying asset diversity detected.',
                    'action': 'Generate strategies for additional underlying assets (NIFTY, BANKNIFTY, etc.).'
                })

            return recommendations

        except Exception as e:
            logger.error(f"Failed to generate evolution recommendations: {e}")
            return []

    async def _save_evolution_analytics(self, analytics: Dict[str, Any]):
        """Save evolution analytics to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"evolution_analytics_{timestamp}.json"
            filepath = Path("data/analytics") / filename
            filepath.parent.mkdir(parents=True, exist_ok=True)

            async with aiofiles.open(filepath, 'w') as f:
                await f.write(json.dumps(analytics, indent=2, default=str))

            logger.info(f"[EVOLUTION] Saved evolution analytics to {filepath}")

        except Exception as e:
            logger.error(f"Failed to save evolution analytics: {e}")

    async def get_evolution_summary(self) -> Dict[str, Any]:
        """Get a concise evolution summary for monitoring"""
        try:
            summary = {
                'timestamp': datetime.now().isoformat(),
                'strategy_count': len(self.strategy_definitions),
                'evolution_status': 'active' if self.is_initialized else 'inactive'
            }

            # Add performance summary if available
            performance_data = await self._analyze_strategy_definition_performance()
            if performance_data:
                returns = [perf.get('total_return', 0) for perf in performance_data.values()]
                import numpy as np
                summary.update({
                    'avg_return': np.mean(returns),
                    'best_return': np.max(returns),
                    'worst_return': np.min(returns),
                    'strategies_with_data': len(performance_data)
                })

            return summary

        except Exception as e:
            logger.error(f"Failed to get evolution summary: {e}")
            return {'error': str(e)}

# For backward compatibility, keep some key functions from the original agent
# These will be imported by other modules that depend on them

def calculate_sharpe_ratio_fast(returns):
    """Fast Sharpe ratio calculation"""
    import numpy as np
    if len(returns) <= 1:
        return 0.0
    mean_return = np.mean(returns)
    std_return = np.std(returns)
    return mean_return / max(std_return, 0.001)

def calculate_max_drawdown_fast(returns):
    """Fast maximum drawdown calculation"""
    import numpy as np
    cumulative = np.cumsum(returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = cumulative - running_max
    return np.min(drawdown)

def calculate_win_rate_fast(returns):
    """Fast win rate calculation"""
    import numpy as np
    return np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0.0

def calculate_profit_factor_fast(returns):
    """Fast profit factor calculation"""
    import numpy as np
    gross_profit = np.sum(returns[returns > 0])
    gross_loss = np.abs(np.sum(returns[returns < 0]))
    return gross_profit / max(gross_loss, 0.001)

def calculate_sortino_ratio_fast(returns):
    """Fast Sortino ratio calculation"""
    import numpy as np
    if len(returns) <= 1:
        return 0.0
    mean_return = np.mean(returns)
    downside_returns = returns[returns < 0]
    downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0.001
    return mean_return / downside_std

def calculate_expectancy_fast(returns):
    """Fast expectancy calculation"""
    import numpy as np
    win_rate = calculate_win_rate_fast(returns)
    avg_win = np.mean(returns[returns > 0]) if np.any(returns > 0) else 0
    avg_loss = np.mean(returns[returns < 0]) if np.any(returns < 0) else 0
    return (win_rate * avg_win) + ((1 - win_rate) * avg_loss)

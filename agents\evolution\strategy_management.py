#!/usr/bin/env python3
"""
Strategy Management Module for Options Strategy Evolution

This module handles strategy registry, lifecycle management, and population control.
It provides efficient storage and retrieval of strategies with performance-based management.
"""

import asyncio
import logging
import json
import yaml
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import aiofiles

logger = logging.getLogger(__name__)

class StrategyStatus(Enum):
    """Strategy status enumeration"""
    ACTIVE = "active"
    EXPERIMENTAL = "experimental"
    DEPRECATED = "deprecated"
    DISABLED = "disabled"
    PROMOTED = "promoted"
    DEMOTED = "demoted"

@dataclass
class StrategyConfig:
    """Strategy configuration structure"""
    strategy_id: str
    name: str
    description: str
    parameters: Dict[str, Any]
    entry_conditions: List[str]
    exit_conditions: List[str]
    risk_management: Dict[str, Any]
    market_outlook: str
    volatility_outlook: str
    timeframe: str
    status: StrategyStatus
    parent_id: Optional[str] = None
    version: str = "v1"
    created_at: Optional[datetime] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        result = asdict(self)
        result['status'] = self.status.value
        result['created_at'] = self.created_at.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'StrategyConfig':
        """Create from dictionary"""
        data = data.copy()
        if 'status' in data:
            data['status'] = StrategyStatus(data['status'])
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)

class StrategyManager:
    """Manages strategy registry and lifecycle"""
    
    def __init__(self, performance_analyzer, strategy_definitions_path: Optional[Path] = None):
        self.performance_analyzer = performance_analyzer
        self.strategy_registry: Dict[str, StrategyConfig] = {}
        self.population_size = 100  # Default population size
        self.strategy_definitions_path = strategy_definitions_path
        
        # File paths
        self.data_path = Path("data")
        self.strategies_path = self.data_path / "strategies"
        self.evolution_path = self.data_path / "strategy_evolution"
        self.registry_path = self.evolution_path / "registry"
        
        # Create directories
        for path in [self.strategies_path, self.evolution_path, self.registry_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize strategy manager"""
        try:
            # Load existing strategies
            await self.load_strategy_registry()
            
            logger.info(f"[STRATEGY] Strategy manager initialized with {len(self.strategy_registry)} strategies")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Strategy manager initialization failed: {e}")
            return False
    
    async def load_strategy_registry(self):
        """Load strategy registry from the main strategy definitions used by other agents"""
        try:
            # Load from the main strategy definitions directory (same as other agents)
            await self._load_from_strategy_definitions()

            logger.info(f"[STRATEGY] Loaded {len(self.strategy_registry)} strategies from strategy definitions")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load strategy registry: {e}")

    async def _load_from_strategy_definitions(self):
        """Load strategies from the main strategy definitions directory (same as other agents)"""
        try:
            if self.strategy_definitions_path:
                filepath = self.strategy_definitions_path
            else:
                filepath = Path("data/strategies") / "strategy_definitions.json"

            if not filepath.exists():
                logger.warning(f"[STRATEGY] Strategy definition file not found at {filepath}")
                return

            logger.info(f"[STRATEGY] Loading strategies from {filepath.name}")

            async with aiofiles.open(filepath, 'r', encoding='utf-8') as f:
                content = await f.read()
                strategy_definitions = json.loads(content)

            loaded_count = 0
            for strategy_def in strategy_definitions:
                try:
                    # Convert strategy definition format to StrategyConfig format
                    strategy_config = {
                        'strategy_id': strategy_def.get('strategy_id', f"strategy_{loaded_count}"),
                        'name': strategy_def.get('name', 'Strategy'),
                        'description': strategy_def.get('description', 'Strategy from definitions'),
                        'parameters': {
                            'strategy_type': strategy_def.get('strategy_type', 'LONG_CALL'),
                            'underlying': strategy_def.get('underlying', 'NIFTY'),
                            'selection_criteria': strategy_def.get('selection_criteria', {}),
                            'market_outlook': strategy_def.get('market_outlook', 'neutral'),
                            'volatility_outlook': strategy_def.get('volatility_outlook', 'normal')
                        },
                        'entry_conditions': strategy_def.get('entry_conditions', []),
                        'exit_conditions': strategy_def.get('exit_conditions', []),
                        'risk_management': strategy_def.get('risk_management', {}),
                        'market_outlook': strategy_def.get('market_outlook', 'neutral'),
                        'volatility_outlook': strategy_def.get('volatility_outlook', 'normal'),
                        'timeframe': strategy_def.get('timeframe', '5min'),
                        'status': StrategyStatus.ACTIVE,  # Mark loaded strategies as active
                        'created_at': datetime.fromisoformat(strategy_def['created_at']) if 'created_at' in strategy_def else datetime.now(),
                        'tags': strategy_def.get('tags', [])
                    }

                    strategy = StrategyConfig.from_dict(strategy_config)
                    self.strategy_registry[strategy.strategy_id] = strategy
                    loaded_count += 1

                except Exception as e:
                    logger.warning(f"[STRATEGY] Failed to load strategy definition: {e}")

            logger.info(f"[STRATEGY] Loaded {loaded_count} strategies from strategy definitions")

        except Exception as e:
            logger.error(f"[STRATEGY] Failed to load from strategy definitions: {e}")
    

    
    async def save_strategy_registry(self):
        """Save strategy registry to disk and update main strategy definitions"""
        try:
            # Save to internal registry
            registry_file = self.registry_path / "strategy_registry.json"

            # Convert to serializable format
            registry_data = {}
            for strategy_id, strategy in self.strategy_registry.items():
                registry_data[strategy_id] = strategy.to_dict()

            async with aiofiles.open(registry_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(registry_data, indent=2, default=str))

            # Also save to main strategy definitions format for other agents
            await self.save_to_strategy_definitions()

            logger.debug(f"[STRATEGY] Saved {len(self.strategy_registry)} strategies to registry and definitions")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategy registry: {e}")

    async def save_to_strategy_definitions(self):
        """Save strategies to the main strategy definitions format used by other agents"""
        try:
            if self.strategy_definitions_path:
                filepath = self.strategy_definitions_path
            else:
                strategies_path = Path("data/strategies")
                strategies_path.mkdir(parents=True, exist_ok=True)
                filepath = strategies_path / "strategy_definitions.json"

            # Convert StrategyConfig objects to strategy definition format
            strategy_definitions = []

            for strategy in self.strategy_registry.values():
                # Convert to strategy definition format
                strategy_def = {
                    'strategy_id': strategy.strategy_id,
                    'strategy_type': strategy.parameters.get('strategy_type', 'LONG_CALL'),
                    'underlying': strategy.parameters.get('underlying', 'NIFTY'),
                    'name': strategy.name,
                    'description': strategy.description,
                    'selection_criteria': strategy.parameters.get('selection_criteria', {
                        'call_leg': {
                            'moneyness': 'ATM',
                            'dte_range': [7, 30],
                            'delta_range': [0.4, 0.6],
                            'volume_threshold': 1000,
                            'open_interest_threshold': 500,
                            'max_bid_ask_spread': 0.05
                        }
                    }),
                    'entry_conditions': strategy.entry_conditions,
                    'exit_conditions': strategy.exit_conditions,
                    'risk_management': strategy.risk_management,
                    'market_outlook': strategy.market_outlook,
                    'volatility_outlook': strategy.volatility_outlook,
                    'timeframe': strategy.timeframe,
                    'created_at': strategy.created_at.isoformat() if strategy.created_at else datetime.now().isoformat(),
                    'tags': strategy.tags or []
                }
                strategy_definitions.append(strategy_def)

            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(strategy_definitions, indent=2, default=str))

            logger.info(f"[STRATEGY] Saved {len(strategy_definitions)} strategies to {filepath.name}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to save to strategy definitions: {e}")

    async def _archive_old_strategy_definitions(self):
        """Archive old strategy definition files"""
        try:
            strategies_path = Path("data/strategies")
            archive_path = strategies_path / "archive"
            archive_path.mkdir(exist_ok=True)

            # Find old strategy definition files (keep only the latest 3)
            definition_files = list(strategies_path.glob("strategy_definitions_*.json"))
            if len(definition_files) > 3:
                # Sort by modification time and archive older files
                definition_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                files_to_archive = definition_files[3:]  # Keep latest 3

                for old_file in files_to_archive:
                    archive_file = archive_path / old_file.name
                    old_file.rename(archive_file)
                    logger.debug(f"[STRATEGY] Archived {old_file.name}")

        except Exception as e:
            logger.debug(f"[STRATEGY] Failed to archive old files: {e}")
    
    async def add_strategy(self, strategy: StrategyConfig) -> bool:
        """Add a strategy to the registry"""
        try:
            if strategy.strategy_id in self.strategy_registry:
                logger.warning(f"[STRATEGY] Strategy {strategy.strategy_id} already exists, updating")
            
            self.strategy_registry[strategy.strategy_id] = strategy
            
            # Save to disk
            await self.save_strategy_registry()
            
            logger.debug(f"[STRATEGY] Added strategy {strategy.strategy_id} to registry")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add strategy {strategy.strategy_id}: {e}")
            return False
    
    async def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy from the registry"""
        try:
            if strategy_id not in self.strategy_registry:
                logger.warning(f"[STRATEGY] Strategy {strategy_id} not found in registry")
                return False
            
            del self.strategy_registry[strategy_id]
            
            # Remove from performance cache as well
            if strategy_id in self.performance_analyzer.performance_cache:
                del self.performance_analyzer.performance_cache[strategy_id]
            
            # Save to disk
            await self.save_strategy_registry()
            
            logger.info(f"[STRATEGY] Removed strategy {strategy_id} from registry")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to remove strategy {strategy_id}: {e}")
            return False
    
    async def get_strategy(self, strategy_id: str) -> Optional[StrategyConfig]:
        """Get a strategy by ID"""
        return self.strategy_registry.get(strategy_id)
    
    async def get_strategies_by_status(self, status: StrategyStatus) -> List[StrategyConfig]:
        """Get strategies by status"""
        return [strategy for strategy in self.strategy_registry.values() if strategy.status == status]
    
    async def get_active_strategies(self) -> List[StrategyConfig]:
        """Get all active strategies"""
        return await self.get_strategies_by_status(StrategyStatus.ACTIVE)
    
    async def get_experimental_strategies(self) -> List[StrategyConfig]:
        """Get all experimental strategies"""
        return await self.get_strategies_by_status(StrategyStatus.EXPERIMENTAL)
    
    async def control_population_size(self):
        """Control population size by removing worst performers"""
        try:
            if len(self.strategy_registry) <= self.population_size:
                return
            
            excess = len(self.strategy_registry) - self.population_size
            logger.info(f"[POPULATION] Population size {len(self.strategy_registry)} exceeds limit {self.population_size}, removing {excess} worst performers")
            
            # Get experimental strategies with their performance scores
            experimental_strategies = []
            
            for strategy_id, strategy in self.strategy_registry.items():
                if strategy.status != StrategyStatus.EXPERIMENTAL:
                    continue
                
                # Get performance score
                if strategy_id in self.performance_analyzer.performance_cache:
                    metrics = self.performance_analyzer.performance_cache[strategy_id]
                    experimental_strategies.append((strategy_id, metrics.composite_score))
                else:
                    # No performance data - mark for removal
                    experimental_strategies.append((strategy_id, -100.0))
            
            if not experimental_strategies:
                logger.warning("[POPULATION] No experimental strategies available for removal")
                return
            
            # Sort by performance score (ascending - worst first)
            experimental_strategies.sort(key=lambda x: x[1])
            
            # Remove worst performers
            removed_count = 0
            for strategy_id, score in experimental_strategies:
                if removed_count >= excess:
                    break
                
                await self.remove_strategy(strategy_id)
                logger.info(f"[POPULATION] Removed worst performer {strategy_id} (score: {score:.3f})")
                removed_count += 1
            
            logger.info(f"[POPULATION] Removed {removed_count} strategies, new population size: {len(self.strategy_registry)}")
            
        except Exception as e:
            logger.error(f"[ERROR] Population control failed: {e}")
            raise RuntimeError(f"Population control failed: {e}")
    
    async def promote_strategy(self, strategy_id: str) -> bool:
        """Promote a strategy from experimental to active"""
        try:
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                logger.error(f"[STRATEGY] Strategy {strategy_id} not found for promotion")
                return False
            
            strategy.status = StrategyStatus.PROMOTED
            await self.save_strategy_registry()
            
            logger.info(f"[STRATEGY] Promoted strategy {strategy_id} to active status")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to promote strategy {strategy_id}: {e}")
            return False
    
    async def demote_strategy(self, strategy_id: str) -> bool:
        """Demote a strategy from active to experimental"""
        try:
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                logger.error(f"[STRATEGY] Strategy {strategy_id} not found for demotion")
                return False
            
            strategy.status = StrategyStatus.DEMOTED
            await self.save_strategy_registry()
            
            logger.info(f"[STRATEGY] Demoted strategy {strategy_id} to experimental status")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to demote strategy {strategy_id}: {e}")
            return False
    
    async def export_strategies_to_definitions(self, output_path: str = None):
        """Export strategies to strategy definitions format for use by other agents"""
        try:
            # Default to the main strategy_definitions.json file or the path provided during initialization
            if output_path is None:
                if self.strategy_definitions_path:
                    output_path = str(self.strategy_definitions_path)
                else:
                    output_path = "data/strategies/strategy_definitions.json"

            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # Convert all strategies to strategy definition format
            strategy_definitions = []
            for strategy in self.strategy_registry.values():
                # Convert to strategy definition format
                strategy_def = {
                    'strategy_id': strategy.strategy_id,
                    'strategy_type': strategy.parameters.get('strategy_type', 'LONG_CALL'),
                    'underlying': strategy.parameters.get('underlying', 'NIFTY'),
                    'name': strategy.name,
                    'description': strategy.description,
                    'selection_criteria': strategy.parameters.get('selection_criteria', {
                        'call_leg': {
                            'moneyness': 'ATM',
                            'dte_range': [7, 30],
                            'delta_range': [0.4, 0.6],
                            'volume_threshold': 1000,
                            'open_interest_threshold': 500,
                            'max_bid_ask_spread': 0.05
                        }
                    }),
                    'entry_conditions': strategy.entry_conditions,
                    'exit_conditions': strategy.exit_conditions,
                    'risk_management': strategy.risk_management,
                    'market_outlook': strategy.market_outlook,
                    'volatility_outlook': strategy.volatility_outlook,
                    'timeframe': strategy.timeframe,
                    'created_at': strategy.created_at.isoformat() if strategy.created_at else datetime.now().isoformat(),
                    'tags': strategy.tags or []
                }
                strategy_definitions.append(strategy_def)

            # Export to JSON (strategy definitions format)
            async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(strategy_definitions, indent=2, default=str))

            logger.info(f"[EXPORT] Exported {len(strategy_definitions)} strategies to {output_path}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to export strategies: {e}")
            return False

    async def export_strategies_to_yaml(self, output_path: str = None) -> bool:
        """Export strategies to YAML format for compatibility with other agents"""
        try:
            import yaml

            if output_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"data/strategies/evolved_strategies_{timestamp}.yaml"

            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # Convert strategies to YAML-compatible format
            strategies_data = []
            for strategy in self.strategy_registry.values():
                strategy_data = {
                    'strategy_id': strategy.strategy_id,
                    'name': strategy.name,
                    'description': strategy.description,
                    'strategy_type': strategy.parameters.get('strategy_type', 'LONG_CALL'),
                    'underlying': strategy.parameters.get('underlying', 'NIFTY'),
                    'selection_criteria': strategy.parameters.get('selection_criteria', {}),
                    'entry_conditions': strategy.entry_conditions,
                    'exit_conditions': strategy.exit_conditions,
                    'risk_management': strategy.risk_management,
                    'market_outlook': strategy.market_outlook,
                    'volatility_outlook': strategy.volatility_outlook,
                    'timeframe': strategy.timeframe,
                    'created_at': strategy.created_at.isoformat() if strategy.created_at else datetime.now().isoformat(),
                    'tags': strategy.tags or [],
                    'status': strategy.status.value if strategy.status else 'experimental'
                }
                strategies_data.append(strategy_data)

            # Export to YAML
            async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
                yaml_content = yaml.dump(strategies_data, default_flow_style=False, indent=2)
                await f.write(yaml_content)

            logger.info(f"[EXPORT] Exported {len(strategies_data)} strategies to {output_path}")
            return True

        except ImportError:
            logger.warning("[EXPORT] PyYAML not available, falling back to JSON export")
            # Fallback to JSON export if YAML is not available
            json_path = output_path.replace('.yaml', '.json') if output_path else None
            return await self.export_strategies_to_definitions(json_path)

        except Exception as e:
            logger.error(f"[ERROR] Failed to export strategies to YAML: {e}")
            return False
    
    def get_registry_summary(self) -> Dict[str, Any]:
        """Get summary of strategy registry"""
        try:
            status_counts = {}
            for status in StrategyStatus:
                status_counts[status.value] = sum(1 for s in self.strategy_registry.values() if s.status == status)
            
            return {
                'total_strategies': len(self.strategy_registry),
                'status_breakdown': status_counts,
                'population_limit': self.population_size,
                'population_usage': f"{len(self.strategy_registry)}/{self.population_size}",
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get registry summary: {e}")
            return {'error': str(e)}

#!/usr/bin/env python3
"""
Strategy Definition Backtester - Backtests strategy definitions by simulating option selection

This module takes strategy definitions and historical market data to:
1. Evaluate entry conditions against historical data
2. Simulate option selection based on selection criteria
3. Track performance of the strategy definition approach
"""

import logging
import polars as pl
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from agents.strategy_generation.data_models import StrategyDefinition
from agents.signal_generation.option_selector import OptionSelector
from agents.backtesting.config import BacktestResults

logger = logging.getLogger(__name__)

class StrategyDefinitionBacktester:
    """Backtests strategy definitions by simulating option selection"""
    
    def __init__(self):
        self.option_selector = OptionSelector()
        self.logger = logger
        
    async def backtest_strategy_definitions(self, strategy_definitions: List[StrategyDefinition],
                                          historical_data: pl.DataFrame,
                                          historical_option_chains: Dict[str, pl.DataFrame],
                                          start_date: str, end_date: str) -> Dict[str, BacktestResults]:
        """
        Backtest strategy definitions using historical data
        
        Args:
            strategy_definitions: List of strategy definitions to test
            historical_data: Historical market data with indicators
            historical_option_chains: Historical option chain data
            start_date: Start date for backtesting
            end_date: End date for backtesting
            
        Returns:
            Dictionary of backtest results for each strategy
        """
        try:
            results = {}
            
            for strategy_def in strategy_definitions:
                logger.info(f"[BACKTEST] Testing strategy definition: {strategy_def.strategy_id}")
                
                result = await self._backtest_single_strategy_definition(
                    strategy_def, historical_data, historical_option_chains, start_date, end_date
                )
                
                if result:
                    results[strategy_def.strategy_id] = result
                    logger.info(f"[BACKTEST] Completed backtest for {strategy_def.strategy_id}")
                else:
                    logger.warning(f"[BACKTEST] Failed to backtest {strategy_def.strategy_id}")
            
            logger.info(f"[BACKTEST] Completed backtesting for {len(results)} strategy definitions")
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to backtest strategy definitions: {e}")
            return {}
    
    async def _backtest_single_strategy_definition(self, strategy_def: StrategyDefinition,
                                                 historical_data: pl.DataFrame,
                                                 historical_option_chains: Dict[str, pl.DataFrame],
                                                 start_date: str, end_date: str) -> Optional[BacktestResults]:
        """Backtest a single strategy definition"""
        try:
            underlying = strategy_def.underlying

            # Convert string dates to datetime for proper comparison
            from datetime import datetime, timezone
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)

            # Filter data for the underlying and date range
            underlying_data = historical_data.filter(
                (pl.col('underlying') == underlying) &
                (pl.col('timestamp') >= start_dt) &
                (pl.col('timestamp') <= end_dt)
            ).sort('timestamp')
            
            if underlying_data.height == 0:
                logger.warning(f"[BACKTEST] No historical data for {underlying} in date range")
                return None
            
            # Get option chain data for this underlying
            if underlying not in historical_option_chains:
                logger.warning(f"[BACKTEST] No option chain data for {underlying}")
                return None
            
            option_chain_data = historical_option_chains[underlying]
            
            # Simulate trading based on strategy definition
            trades = await self._simulate_trades(strategy_def, underlying_data, option_chain_data)
            
            if not trades:
                logger.warning(f"[BACKTEST] No trades generated for {strategy_def.strategy_id}")
                return None
            
            # Calculate performance metrics
            performance = await self._calculate_performance_metrics(trades, strategy_def)
            
            return performance
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to backtest strategy {strategy_def.strategy_id}: {e}")
            return None
    
    async def _simulate_trades(self, strategy_def: StrategyDefinition,
                             historical_data: pl.DataFrame,
                             option_chain_data: pl.DataFrame) -> List[Dict]:
        """Simulate trades based on strategy definition"""
        try:
            trades = []
            
            # Process data day by day to simulate real trading
            dates = historical_data.select('timestamp').to_series().dt.date().unique().sort()

            for date in dates:
                # Get data for this date
                daily_data = historical_data.filter(pl.col('timestamp').dt.date() == date)

                if daily_data.height == 0:
                    continue

                # Get option chain for this date - convert Python date to pl.Date for proper comparison
                daily_option_chain = option_chain_data.filter(pl.col('date') == date)
                
                if daily_option_chain.height == 0:
                    continue
                
                # Check for entry signals throughout the day
                for i in range(daily_data.height):
                    current_data = daily_data.slice(max(0, i-20), i+1)  # Use last 20 periods for indicators
                    
                    if current_data.height == 0:
                        continue
                    
                    # Evaluate entry conditions
                    entry_signal = await self._evaluate_entry_conditions_backtest(
                        strategy_def.entry_conditions, current_data
                    )
                    
                    if entry_signal:
                        # Get current price
                        current_row = current_data.tail(1).row(0, named=True)
                        current_price = current_row.get('close', current_row.get('ltp', 0))
                        
                        if current_price <= 0:
                            continue
                        
                        # Select options based on criteria
                        selected_options = await self.option_selector.select_options_for_strategy(
                            strategy_def, daily_option_chain, current_price
                        )
                        
                        if selected_options:
                            # Create trade entry
                            trade = await self._create_trade_entry(
                                strategy_def, selected_options, current_row, current_price
                            )
                            
                            if trade:
                                trades.append(trade)
                                logger.debug(f"[TRADE] Entry: {strategy_def.strategy_id} at {current_row.get('timestamp')}")
            
            # Process trade exits
            completed_trades = await self._process_trade_exits(trades, historical_data, strategy_def)
            
            logger.info(f"[BACKTEST] Generated {len(completed_trades)} completed trades for {strategy_def.strategy_id}")
            return completed_trades
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to simulate trades: {e}")
            return []
    
    async def _evaluate_entry_conditions_backtest(self, entry_conditions: List[Dict],
                                                market_data: pl.DataFrame) -> bool:
        """Evaluate entry conditions for backtesting"""
        try:
            if not entry_conditions:
                return True
            
            latest_row = market_data.tail(1).row(0, named=True)
            
            for condition in entry_conditions:
                indicator = condition.get('indicator')
                operator = condition.get('operator')
                value = condition.get('value')
                
                # Get indicator value
                if indicator in latest_row:
                    indicator_value = latest_row[indicator]
                elif indicator == "RSI":
                    indicator_value = latest_row.get('rsi_14', latest_row.get('rsi', 50))
                elif indicator == "Price":
                    indicator_value = latest_row.get('close', latest_row.get('ltp', 0))
                else:
                    continue
                
                # Handle special values
                if isinstance(value, str):
                    if value == "SMA_20":
                        value = latest_row.get('sma_20', indicator_value)
                    elif "avg_volume" in value and "*" in value:
                        multiplier = float(value.split('*')[-1].strip())
                        value = latest_row.get('avg_volume_10d', latest_row.get('volume', 0)) * multiplier
                
                # Evaluate condition
                if operator == ">" and indicator_value <= value:
                    return False
                elif operator == "<" and indicator_value >= value:
                    return False
                elif operator == ">=" and indicator_value < value:
                    return False
                elif operator == "<=" and indicator_value > value:
                    return False
                elif operator == "break_above" and indicator_value <= value:
                    return False
                elif operator == "break_below" and indicator_value >= value:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to evaluate entry conditions: {e}")
            return False
    
    async def _create_trade_entry(self, strategy_def: StrategyDefinition,
                                selected_options: List,
                                market_row: Dict,
                                current_price: float) -> Optional[Dict]:
        """Create a trade entry record"""
        try:
            primary_option = selected_options[0]
            
            trade = {
                'strategy_id': strategy_def.strategy_id,
                'entry_time': market_row.get('timestamp'),
                'underlying_price': current_price,
                'option_symbol': primary_option.symbol,
                'option_type': primary_option.option_type,
                'strike_price': primary_option.strike_price,
                'entry_premium': primary_option.premium,
                'quantity': primary_option.quantity,
                'stop_loss': strategy_def.risk_management.get('stop_loss', 0.5),
                'take_profit': strategy_def.risk_management.get('take_profit', 1.0),
                'position_size': strategy_def.risk_management.get('position_size', 0.05),
                'exit_time': None,
                'exit_premium': None,
                'exit_reason': None,
                'pnl': None,
                'return_pct': None
            }
            
            return trade
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to create trade entry: {e}")
            return None
    
    async def _process_trade_exits(self, trades: List[Dict],
                                 historical_data: pl.DataFrame,
                                 strategy_def: StrategyDefinition) -> List[Dict]:
        """Process trade exits based on exit conditions"""
        try:
            completed_trades = []
            
            for trade in trades:
                # Find exit point for this trade
                entry_time = trade['entry_time']
                
                # Get data after entry time
                exit_data = historical_data.filter(pl.col('timestamp') > entry_time).sort('timestamp')
                
                if exit_data.height == 0:
                    continue
                
                # Check exit conditions
                exit_info = await self._find_exit_point(trade, exit_data, strategy_def)
                
                if exit_info:
                    trade.update(exit_info)
                    completed_trades.append(trade)
            
            return completed_trades
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to process trade exits: {e}")
            return []
    
    async def _find_exit_point(self, trade: Dict, exit_data: pl.DataFrame,
                             strategy_def: StrategyDefinition) -> Optional[Dict]:
        """Find the exit point for a trade"""
        try:
            entry_premium = trade['entry_premium']
            stop_loss_level = entry_premium * (1 - trade['stop_loss'])
            take_profit_level = entry_premium * (1 + trade['take_profit'])
            
            # Simulate option price movement (simplified)
            for i in range(exit_data.height):
                row = exit_data.slice(i, 1).row(0, named=True)
                
                # Simplified option price simulation based on underlying movement
                underlying_price = row.get('close', row.get('ltp', 0))
                entry_underlying = trade['underlying_price']
                
                if underlying_price <= 0 or entry_underlying <= 0:
                    continue
                
                # Simple delta approximation for option price
                price_change_pct = (underlying_price - entry_underlying) / entry_underlying
                
                if trade['option_type'] == 'CE':
                    # Call option - benefits from price increase
                    option_price_change = price_change_pct * 2  # Simplified leverage
                else:
                    # Put option - benefits from price decrease
                    option_price_change = -price_change_pct * 2
                
                current_premium = entry_premium * (1 + option_price_change)
                current_premium = max(0.1, current_premium)  # Minimum premium
                
                # Check exit conditions
                if current_premium <= stop_loss_level:
                    return {
                        'exit_time': row.get('timestamp'),
                        'exit_premium': current_premium,
                        'exit_reason': 'stop_loss',
                        'pnl': (current_premium - entry_premium) * trade['quantity'],
                        'return_pct': (current_premium - entry_premium) / entry_premium
                    }
                elif current_premium >= take_profit_level:
                    return {
                        'exit_time': row.get('timestamp'),
                        'exit_premium': current_premium,
                        'exit_reason': 'take_profit',
                        'pnl': (current_premium - entry_premium) * trade['quantity'],
                        'return_pct': (current_premium - entry_premium) / entry_premium
                    }
            
            # If no exit condition met, exit at last available price
            if exit_data.height > 0:
                last_row = exit_data.tail(1).row(0, named=True)
                underlying_price = last_row.get('close', last_row.get('ltp', 0))
                
                if underlying_price > 0:
                    entry_underlying = trade['underlying_price']
                    price_change_pct = (underlying_price - entry_underlying) / entry_underlying
                    
                    if trade['option_type'] == 'CE':
                        option_price_change = price_change_pct * 2
                    else:
                        option_price_change = -price_change_pct * 2
                    
                    current_premium = entry_premium * (1 + option_price_change)
                    current_premium = max(0.1, current_premium)
                    
                    return {
                        'exit_time': last_row.get('timestamp'),
                        'exit_premium': current_premium,
                        'exit_reason': 'end_of_data',
                        'pnl': (current_premium - entry_premium) * trade['quantity'],
                        'return_pct': (current_premium - entry_premium) / entry_premium
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to find exit point: {e}")
            return None
    
    async def _calculate_performance_metrics(self, trades: List[Dict],
                                           strategy_def: StrategyDefinition) -> BacktestResults:
        """Calculate performance metrics from completed trades"""
        try:
            if not trades:
                return BacktestResults(
                    strategy_id=strategy_def.strategy_id,
                    total_trades=0,
                    winning_trades=0,
                    losing_trades=0,
                    total_return=0.0,
                    max_drawdown=0.0,
                    sharpe_ratio=0.0,
                    win_rate=0.0,
                    avg_return_per_trade=0.0,
                    max_consecutive_losses=0,
                    profit_factor=0.0
                )
            
            # Calculate basic metrics
            total_trades = len(trades)
            returns = [trade['return_pct'] for trade in trades if trade['return_pct'] is not None]
            
            if not returns:
                returns = [0.0]
            
            winning_trades = len([r for r in returns if r > 0])
            losing_trades = len([r for r in returns if r < 0])
            
            total_return = sum(returns)
            avg_return = np.mean(returns)
            
            # Calculate win rate
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # Calculate Sharpe ratio (simplified)
            if len(returns) > 1:
                sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            else:
                sharpe_ratio = 0
            
            # Calculate max drawdown (simplified)
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = cumulative_returns - running_max
            max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0
            
            # Calculate profit factor
            gross_profit = sum([r for r in returns if r > 0])
            gross_loss = abs(sum([r for r in returns if r < 0]))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Calculate max consecutive losses
            consecutive_losses = 0
            max_consecutive_losses = 0
            for ret in returns:
                if ret < 0:
                    consecutive_losses += 1
                    max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
                else:
                    consecutive_losses = 0
            
            return BacktestResults(
                strategy_id=strategy_def.strategy_id,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                total_return=total_return,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                win_rate=win_rate,
                avg_return_per_trade=avg_return,
                max_consecutive_losses=max_consecutive_losses,
                profit_factor=profit_factor
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate performance metrics: {e}")
            return BacktestResults(
                strategy_id=strategy_def.strategy_id,
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                total_return=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                win_rate=0.0,
                avg_return_per_trade=0.0,
                max_consecutive_losses=0,
                profit_factor=0.0
            )
